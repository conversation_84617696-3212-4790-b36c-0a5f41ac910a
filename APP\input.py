from typing import Optional, Dict, List, Union
from pydantic import BaseModel, Field
from datetime import datetime, timezone
import uuid
import imaplib
import email
from email.parser import BytesParser
from email.message import EmailMessage
import os
import logging
from typing import Callable
import time
import asyncio
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InputObject(BaseModel):
    """Standardized input object for all incoming data to Daeira"""
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    source_type: str
    source_details: Dict[str, Union[str, int, None]]
    content_type: str
    content: Union[str, Dict]
    priority: Optional[int] = None
    initial_tags: Optional[List[str]] = None

def _load_config():
    """Load email configuration with explicit values (no env, no None)"""
    return {
        'IMAP_SERVER': 'imap.dreamhost.com',
        'IMAP_PORT': 993,
        'IMAP_USERNAME': '<EMAIL>',
        'IMAP_PASSWORD': 'lifecycleevent02025!?',
        'SMTP_SERVER': 'smtp.dreamhost.com',
        'SMTP_PORT': 465,
        'EMAIL_TARGET_FOLDER': 'INBOX',
        'EMAIL_PROCESSED_FOLDER': 'Processed/Daeira',
        'EMAIL_ERROR_FOLDER': 'Errors/Daeira',
        'POLLING_INTERVAL_SECONDS': 3000
    }

def _parse_email_body(email_message: EmailMessage) -> tuple[str, str]:
    """Extract and clean the email body content"""
    email_body_content = ""
    email_content_type = "text/plain"
    
    if email_message.is_multipart():
        for part in email_message.walk():
            content_type = part.get_content_type()
            if content_type == "text/plain" and not part.get("Content-Disposition"):
                email_body_content = part.get_payload(decode=True).decode(
                    part.get_content_charset(failobj='utf-8'), 'ignore'
                )
                break
            elif content_type == "text/html" and not email_body_content:
                # TODO: Add HTML to text conversion if needed
                html_content = part.get_payload(decode=True).decode(
                    part.get_content_charset(failobj='utf-8'), 'ignore'
                )
                email_body_content = html_content  # For now, just use raw HTML
                email_content_type = "text/html"
    else:
        email_body_content = email_message.get_payload(decode=True).decode(
            email_message.get_content_charset(failobj='utf-8'), 'ignore'
        )
        email_content_type = email_message.get_content_type()
    
    return email_body_content.strip(), email_content_type

def fetch_and_process_emails(dispatch_callback: Callable[[InputObject], None], event_loop: asyncio.AbstractEventLoop = None):
    """
    Fetch and process new emails from the IMAP server
    """
    config = _load_config()
    logger.info("Starting email fetch and process cycle")
    
    mail_server = None
    try:
        # Connect to IMAP server
        mail_server = imaplib.IMAP4_SSL(config['IMAP_SERVER'], config['IMAP_PORT'])
        mail_server.login(config['IMAP_USERNAME'], config['IMAP_PASSWORD'])
        
        # Select mailbox
        status, messages = mail_server.select(config['EMAIL_TARGET_FOLDER'])
        if status != 'OK':
            raise imaplib.IMAP4.error(f"Failed to select folder: {config['EMAIL_TARGET_FOLDER']}")
        
        # Search for unseen emails
        status, email_ids = mail_server.search(None, 'UNSEEN')
        if status != 'OK':
            raise imaplib.IMAP4.error("Failed to search for unseen emails")
        
        email_id_list = email_ids[0].split()
        if not email_id_list:
            logger.info("No new emails found")
            return
        
        logger.info(f"Found {len(email_id_list)} new email(s)")
        
        # Process each email
        for email_id in email_id_list:
            try:
                # Fetch email data
                status, msg_data = mail_server.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    raise imaplib.IMAP4.error(f"Failed to fetch email {email_id}")
                
                email_message = email.message_from_bytes(msg_data[0][1])
                
                # Parse email content
                body_content, content_type = _parse_email_body(email_message)
                
                # Create InputObject
                input_obj = InputObject(
                    source_type='email',
                    source_details={
                        'from_address': email_message['From'],
                        'to_address': email_message['To'],
                        'subject': email_message['Subject'],
                        'message_id': email_message['Message-ID']
                    },
                    content_type=content_type,
                    content=body_content,
                    priority=5  # Default priority for emails
                )
                
                # Dispatch the input object
                if event_loop and asyncio.iscoroutinefunction(dispatch_callback):
                    # Schedule the coroutine on the main event loop
                    future = asyncio.run_coroutine_threadsafe(dispatch_callback(input_obj), event_loop)
                    # Optionally wait for completion with timeout
                    try:
                        future.result(timeout=30)  # 30 second timeout
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout waiting for dispatch callback for email {input_obj.event_id}")
                else:
                    # Synchronous callback
                    dispatch_callback(input_obj)
                
                logger.info(f"Processed email: {input_obj.event_id}")
                
                # Mark as processed
                mail_server.store(email_id, '+FLAGS', '\\Seen')
                
            except Exception as e:
                logger.error(f"Error processing email {email_id}: {str(e)}", exc_info=True)
                
    except Exception as e:
        logger.error(f"Error in email processing: {str(e)}", exc_info=True)
        
    finally:
        if mail_server:
            try:
                mail_server.close()
                mail_server.logout()
            except:
                pass

def start_email_polling(dispatch_callback: Callable[[InputObject], None], event_loop: asyncio.AbstractEventLoop = None):
    """Start polling for new emails"""
    config = _load_config()
    interval = config['POLLING_INTERVAL_SECONDS']
    
    logger.info(f"Starting email polling every {interval} seconds")
    
    while True:
        try:
            fetch_and_process_emails(dispatch_callback, event_loop)
        except Exception as e:
            logger.error(f"Error in polling cycle: {str(e)}", exc_info=True)
        
        time.sleep(interval)

def start_email_polling_thread(dispatch_callback: Callable[[InputObject], None], event_loop: asyncio.AbstractEventLoop = None):
    """Start email polling in a separate thread"""
    def polling_worker():
        start_email_polling(dispatch_callback, event_loop)
    
    thread = threading.Thread(target=polling_worker, daemon=True)
    thread.start()
    logger.info("Email polling thread started")
    return thread