from typing import Optional, Dict, List, Union
from pydantic import BaseModel, Field
from datetime import datetime, timezone
import uuid
import logging
import asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InputObject(BaseModel):
    """Standardized input object for all incoming data to Daeira"""
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    source_type: str
    source_details: Dict[str, Union[str, int, None]]
    content_type: str
    content: Union[str, Dict]
    priority: Optional[int] = None
    initial_tags: Optional[List[str]] = None

async def process_input(input_obj, model_instance=None, tokenizer_instance=None, memory_manager=None):
    """Enhanced input processing dispatcher"""
    try:
        if input_obj.source_type == 'email':
            # Import here to avoid circular imports
            from email_service import process_email
            await process_email(
                input_obj, model_instance, tokenizer_instance, memory_manager
            )
        else:
            # Handle other input types with current evaluation system
            from evaluate import evaluate_input
            evaluation_result = await evaluate_input(
                input_obj,
                model_instance,
                tokenizer_instance,
                memory_manager
            )

            logger.info(f"Input {input_obj.event_id} evaluated: {evaluation_result}")

            if memory_manager:
                memory_manager.structured_store.update_input_object_post_evaluation(
                    input_obj.event_id,
                    f"Non-email input processed: {evaluation_result}",
                    []
                )

    except Exception as e:
        logger.error(f"Error processing input {input_obj.event_id}: {str(e)}", exc_info=True)