"""
DAHLIA 3-Inference Cognitive Cycle
Simple elegance: Complete conversation history → 3 progressive cognitive passes → Response
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from input import InputObject
from prompt_logger import save_prompt, save_inference_result

logger = logging.getLogger(__name__)

async def evaluate_input(
    input_obj: InputObject,
    model_instance,
    tokenizer_instance,
    memory_manager,
    conversation_history: Optional[List[Dict[str, str]]] = None
) -> str:
    """
    DAHLIA 3-Inference Cognitive Cycle:
    Each inference processes complete conversation history with increasing depth
    
    Cycle 1: "What should I do?" - Basic recognition and response formulation
    Cycle 2: "What should I do? How should I do it?" - Strategic thinking added
    Cycle 3: Full deliberation - Deep, nuanced understanding with extensive processing
    
    Returns: Final response after 3-cycle cognitive processing
    """
    
    try:
        # Build complete conversation history (first user input is the "system prompt")
        full_history = _build_complete_history(conversation_history or [], input_obj)

        # Save the initial prompt
        save_prompt(
            prompt_text=full_history,
            prompt_type=f"{input_obj.source_type}_initial",
            metadata={
                "event_id": input_obj.event_id,
                "source_type": input_obj.source_type,
                "content_type": input_obj.content_type
            },
            session_id=input_obj.source_details.get("session_id")
        )

        # preva-DAEIRA_inference (Level 1)
        cycle_1_response = await _cognitive_cycle_1(
            full_history,
            model_instance,
            tokenizer_instance,
            input_obj
        )

        # eva-DAEIRA_inference (Level 2)
        cycle_2_response = await _cognitive_cycle_2(
            full_history,
            model_instance,
            tokenizer_instance,
            input_obj
        )

        # DAEIRA_inference (Level 3)
        final_response = await _cognitive_cycle_3(
            full_history,
            model_instance,
            tokenizer_instance,
            input_obj
        )

        logger.info(f"DAEIRA 3-cycle cognitive processing complete:")
        logger.info(f"  - Cycle 1 (preva-DAEIRA): {len(cycle_1_response)} chars")
        logger.info(f"  - Cycle 2 (eva-DAEIRA): {len(cycle_2_response)} chars")
        logger.info(f"  - Cycle 3 (DAEIRA): {len(final_response)} chars")
        logger.info(f"All inference levels activated with prompt saving enabled")
        return final_response
        
    except Exception as e:
        logger.error(f"Cognitive cycle evaluation failed: {e}")
        # Simple fallback - direct response without cycles
        return await _direct_response_fallback(input_obj, conversation_history or [], model_instance, tokenizer_instance)

async def _cognitive_cycle_1(full_history: str, model_instance, tokenizer_instance, input_obj: InputObject) -> str:
    """
    Cognitive Cycle 1: Basic Recognition
    "What should I do?"
    """

    cycle_1_prompt = f"""{full_history}

DAHLIA: <think>
What should I do?
</think>

"""

    try:
        # Save the cycle 1 prompt
        save_prompt(
            prompt_text=cycle_1_prompt,
            prompt_type=f"{input_obj.source_type}_cycle1",
            metadata={
                "event_id": input_obj.event_id,
                "cycle": 1,
                "source_type": input_obj.source_type,
                "inference_level": "preva-DAEIRA"
            },
            session_id=input_obj.source_details.get("session_id")
        )

        response = await _generate_response(
            cycle_1_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=800
        )

        # Save the complete cycle 1 inference result
        save_inference_result(
            prompt_text=cycle_1_prompt,
            response_text=response,
            inference_type=f"{input_obj.source_type}_cycle1",
            metadata={
                "event_id": input_obj.event_id,
                "cycle": 1,
                "source_type": input_obj.source_type,
                "inference_level": "preva-DAEIRA",
                "response_length": len(response)
            },
            session_id=input_obj.source_details.get("session_id"),
            event_id=input_obj.event_id
        )

        logger.debug(f"Cycle 1 complete: {len(response)} chars")
        return response

    except Exception as e:
        logger.error(f"Cognitive cycle 1 failed: {e}")
        return "Basic recognition processing failed."

async def _cognitive_cycle_2(full_history: str, model_instance, tokenizer_instance, input_obj: InputObject) -> str:
    """
    Cognitive Cycle 2: Strategic Planning
    "What should I do? How should I do it?"
    """

    cycle_2_prompt = f"""{full_history}

DAHLIA: <think>
What should I do?

How should I do it?
</think>

"""

    try:
        # Save the cycle 2 prompt
        save_prompt(
            prompt_text=cycle_2_prompt,
            prompt_type=f"{input_obj.source_type}_cycle2",
            metadata={
                "event_id": input_obj.event_id,
                "cycle": 2,
                "source_type": input_obj.source_type,
                "inference_level": "eva-DAEIRA"
            },
            session_id=input_obj.source_details.get("session_id")
        )

        response = await _generate_response(
            cycle_2_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=1200
        )

        # Save the complete cycle 2 inference result
        save_inference_result(
            prompt_text=cycle_2_prompt,
            response_text=response,
            inference_type=f"{input_obj.source_type}_cycle2",
            metadata={
                "event_id": input_obj.event_id,
                "cycle": 2,
                "source_type": input_obj.source_type,
                "inference_level": "eva-DAEIRA",
                "response_length": len(response)
            },
            session_id=input_obj.source_details.get("session_id"),
            event_id=input_obj.event_id
        )

        logger.debug(f"Cycle 2 complete: {len(response)} chars")
        return response

    except Exception as e:
        logger.error(f"Cognitive cycle 2 failed: {e}")
        return "Strategic planning processing failed."

async def _cognitive_cycle_3(full_history: str, model_instance, tokenizer_instance, input_obj: InputObject) -> str:
    """
    Cognitive Cycle 3: Deep Deliberation
    "What should I do? How should I do it?" + extensive processing
    """
    
    cycle_3_prompt = f"""{full_history}

DAHLIA: <think>
What should I do?

How should I do it?

[Deep cognitive processing - considering full conversational context, relationships, nuances, implications, and optimal response approach]
</think>

"""
    
    try:
        # Save the cycle 3 prompt
        save_prompt(
            prompt_text=cycle_3_prompt,
            prompt_type=f"{input_obj.source_type}_cycle3",
            metadata={
                "event_id": input_obj.event_id,
                "cycle": 3,
                "source_type": input_obj.source_type,
                "inference_level": "DAEIRA"
            },
            session_id=input_obj.source_details.get("session_id")
        )

        response = await _generate_response(
            cycle_3_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=2000
        )

        # Save the complete inference result
        save_inference_result(
            prompt_text=cycle_3_prompt,
            response_text=response,
            inference_type=f"{input_obj.source_type}_cycle3",
            metadata={
                "event_id": input_obj.event_id,
                "cycle": 3,
                "source_type": input_obj.source_type,
                "inference_level": "DAEIRA",
                "response_length": len(response)
            },
            session_id=input_obj.source_details.get("session_id"),
            event_id=input_obj.event_id
        )

        logger.debug(f"Cycle 3 complete: {len(response)} chars")
        return response

    except Exception as e:
        logger.error(f"Cognitive cycle 3 failed: {e}")
        return "Deep deliberation processing failed."

def _build_complete_history(conversation_history: List[Dict[str, str]], current_input: InputObject) -> str:
    """
    Build complete conversation history starting from first user input (system prompt)
    Memory never resets - each cognitive cycle sees the full context
    """
    
    history_parts = []
    
    # Process all conversation history
    for turn in conversation_history:
        role = turn.get("role", "unknown")
        content = turn.get("content", "")
        
        if role.lower() in ["user", "victor"]:
            history_parts.append(f"Victor: {content}")
        elif role.lower() in ["assistant", "dahlia"]:
            history_parts.append(f"DAHLIA: {content}")
        # Skip system messages - first user input serves as system prompt
    
    # Add current input
    source_info = current_input.source_details.get("from", "Victor")
    history_parts.append(f"{source_info}: {current_input.content}")
    
    return "\n".join(history_parts)

async def _generate_response(prompt: str, model_instance, tokenizer_instance, max_tokens: int = 1000) -> str:
    """Generate response using the model with cognitive cycle processing"""
    try:
        import torch
        
        # Tokenize with appropriate context window
        inputs = tokenizer_instance(
            prompt, 
            return_tensors="pt", 
            truncation=True, 
            max_length=32000  # Generous context for full history
        )
        
        with torch.no_grad():
            outputs = model_instance.generate(
                inputs.input_ids,
                max_new_tokens=max_tokens,
                temperature=0.8,  # Slightly higher for creative cognitive processing
                do_sample=True,
                pad_token_id=tokenizer_instance.eos_token_id,
                repetition_penalty=1.1
            )
        
        # Decode only the new tokens (response)
        response = tokenizer_instance.decode(
            outputs[0][inputs.input_ids.shape[1]:], 
            skip_special_tokens=True
        )
        
        return response.strip()
        
    except Exception as e:
        logger.error(f"Model generation failed: {e}")
        return f"Cognitive processing error: {str(e)}"

async def _direct_response_fallback(
    input_obj: InputObject, 
    conversation_history: List[Dict[str, str]], 
    model_instance, 
    tokenizer_instance
) -> str:
    """
    Simple fallback if 3-cycle processing fails entirely
    Still maintains the conversation history approach
    """
    
    try:
        full_history = _build_complete_history(conversation_history, input_obj)
        fallback_prompt = f"""{full_history}

DAHLIA: """
        
        response = await _generate_response(
            fallback_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=1500
        )
        
        logger.warning("Used direct response fallback")
        return response
        
    except Exception as e:
        logger.error(f"Even fallback failed: {e}")
        return "I apologize, but I'm experiencing cognitive processing difficulties. Please try again."

# Additional utility functions for future enhancements

def _extract_thinking_content(response: str) -> tuple[str, str]:
    """
    Extract <think>...</think> content from response
    Returns: (thinking_content, actual_response)
    """
    if "<think>" in response and "</think>" in response:
        start = response.find("<think>") + 7
        end = response.find("</think>")
        thinking = response[start:end].strip()
        actual_response = response[end + 8:].strip()
        return thinking, actual_response
    
    return "", response

def _log_cognitive_insights(cycle_responses: List[str]) -> None:
    """
    Log insights from the 3-cycle cognitive processing
    Useful for understanding DAHLIA's reasoning development
    """
    for i, response in enumerate(cycle_responses, 1):
        thinking, _ = _extract_thinking_content(response)
        if thinking:
            logger.info(f"Cycle {i} cognitive insight: {thinking[:100]}...")
