from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import torch
import uuid
import os
import json
import asyncio
from datetime import datetime, timezone
from typing import List, Optional
from transformers import AutoTokenizer, AutoModelForCausalLM
from evaluate import evaluate_input
from input import InputObject
from app_modules.utils import configure_logger
from prompt_logger import save_chat_prompt, save_inference_result, prompt_logger
# Environment variables removed - using hardcoded configuration

# Import memory management and document processing
from MEMORY_daeira import MemoryManagerDaeira, initialize_memory
from document_processor import EnhancedDocumentProcessor, create_enhanced_document_processor

# Import TTS service
from tts_service import initialize_tts_service, get_tts_service, get_tts_priority, speak_text_async

# Import email service
from email_service import start_email_service, EmailResponseGenerator

logger = configure_logger()

def load_daeira_model(model_path: str):
    """Load model and tokenizer for DAEIRA"""
    logger.info(f"Loading DAEIRA model from: {model_path}")

    tokenizer = AutoTokenizer.from_pretrained(model_path)

    torch_dtype = torch.bfloat16 if torch.cuda.is_available() and torch.cuda.is_bf16_supported() else torch.float32

    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch_dtype,
        trust_remote_code=True,
        attn_implementation="eager"
    )

    device = "cuda" if torch.cuda.is_available() else "cpu"
    model.to(device).eval()

    logger.info(f"DAEIRA model loaded successfully. Device: {device}")
    return tokenizer, model, None  # Return tuple for compatibility

models = load_daeira_model("C:/DAEIRA/deepseek_R1_7B")
conversation_storage_path = "storage/conversations.json"

# Initialize memory manager and document processor
try:
    memory_manager = initialize_memory()
    logger.info("Memory manager initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize memory manager: {e}")
    memory_manager = None

# Initialize document processor with memory manager and model components
try:
    tokenizer, vl_gpt, vl_chat_processor = models
    document_processor = create_enhanced_document_processor(
        memory_manager=memory_manager,
        study_directory="C:/DAEIRA/STUDY",
        model_instance=vl_gpt,
        tokenizer_instance=tokenizer
    )
    logger.info("Enhanced document processor initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize document processor: {e}")
    document_processor = None

# Initialize TTS service with voice preferences
try:
    tts_config = {
        'rate': 180,
        'volume': 0.8,
        'voice_preference': 'susan,hazel',  # Default to 'susan', fallback to 'hazel'
        'max_text_length': 100000
    }
    tts_service = initialize_tts_service(tts_config)
    logger.info("TTS service initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize TTS service: {e}")
    tts_service = None

# Initialize email service
try:
    tokenizer, vl_gpt, vl_chat_processor = models
    manual_email_check = start_email_service(
        model_instance=vl_gpt,
        tokenizer_instance=tokenizer,
        memory_manager=memory_manager
    )
    logger.info("Email service initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize email service: {e}")
    manual_email_check = None

class JsonConversationStorage:
    def __init__(self, filepath):
        self.filepath = filepath
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        if not os.path.exists(filepath):
            with open(filepath, 'w') as f:
                json.dump({}, f)

    def _load(self):
        with open(self.filepath, 'r') as f:
            return json.load(f)

    def _save(self, data):
        with open(self.filepath, 'w') as f:
            json.dump(data, f, indent=2)

    def get_history(self, session_id):
        return self._load().get(session_id, [])

    def save_turn(self, session_id, user_role, user_message, assistant_role, assistant_message):
        data = self._load()
        history = data.get(session_id, [])
        history.extend([
            {"role": user_role, "message": user_message},
            {"role": assistant_role, "message": assistant_message},
        ])
        data[session_id] = history
        self._save(data)

    def delete_last_turn(self, session_id):
        data = self._load()
        history = data.get(session_id, [])
        if len(history) >= 2:
            data[session_id] = history[:-2]
            self._save(data)

    def clear_history(self, session_id):
        data = self._load()
        if session_id in data:
            del data[session_id]
            self._save(data)

conversation_storage = JsonConversationStorage(conversation_storage_path)

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_prompt(conv) -> str:
    ret = ""
    for role, message in conv.messages:
        if role == conv.roles[0]:
            ret += f"{role}: {message}\n"
        elif role == conv.roles[1]:
            ret += f"{role}: {message}\n\n" if message else f"{role}:"
    return ret

def generate_prompt_with_history(text, session_id, vl_chat_processor, tokenizer, max_length=2048):
    history = conversation_storage.get_history(session_id)
    conversation = vl_chat_processor.new_chat_template()
    conversation.roles = ("Victor", "Daeira")
    conversation.get_prompt = lambda: get_prompt(conversation)

    if history:
        conversation.messages = [
            [conversation.roles[0] if entry['role'].lower() in ['user', 'victor'] else conversation.roles[1], entry['message']]
            for entry in history
        ]

    conversation.append_message(conversation.roles[0], text)
    conversation.append_message(conversation.roles[1], "")

    rounds = len(conversation.messages) // 2
    for _ in range(rounds):
        if torch.tensor(models[0].encode(conversation.get_prompt())).size(-1) <= max_length:
            return conversation
        if len(conversation.messages) >= 2:
            conversation.messages = conversation.messages[2:]
        else:
            break
    return conversation

@app.post("/chat")
async def chat(
    prompt: str = Form(...),
    enable_tts: str = Form("false"),
    tts_priority: str = Form("normal"),
    image: UploadFile = File(None),
    session_id: str = Form(default_factory=lambda: str(uuid.uuid4()))
):
    try:
        tokenizer, vl_gpt, _ = models

        # Build conversation history for prompt logging
        history = conversation_storage.get_history(session_id)
        full_prompt = f"Session: {session_id}\n"
        if history:
            for entry in history:
                role = "Victor" if entry['role'].lower() in ['user', 'victor'] else "Daeira"
                full_prompt += f"{role}: {entry['message']}\n"
        full_prompt += f"Victor: {prompt}\nDaeira:"

        # Save the chat prompt
        save_chat_prompt(
            prompt_text=full_prompt,
            session_id=session_id,
            user_input=prompt
        )

        # Create InputObject for the chat using the new system
        chat_input = InputObject(
            source_type='chat',
            source_details={'session_id': session_id},
            content_type='text/plain',
            content=prompt
        )

        # Use evaluate_input (runs all 3 inference levels: preva_Daeira_inference, eva_Daeira_inference, Daeira_inference)
        response_text = await evaluate_input(
            input_obj=chat_input,
            model_instance=vl_gpt,
            tokenizer_instance=tokenizer,
            memory_manager=memory_manager
        )

        # Save the complete chat inference result
        save_inference_result(
            prompt_text=full_prompt,
            response_text=response_text,
            inference_type="chat_complete",
            metadata={
                "session_id": session_id,
                "user_input": prompt,
                "has_history": len(history) > 0 if history else False
            },
            session_id=session_id,
            event_id=chat_input.event_id
        )

        # Save conversation turn
        conversation_storage.save_turn(session_id, "User", prompt, "Daeira", response_text)

        # Auto-play TTS for model response (always enabled)
        tts_message_id = None
        if response_text and response_text.strip():
            try:
                # Always enable TTS for model responses with specified priority
                tts_message_id = await speak_text_async(response_text, tts_priority)
                if tts_message_id:
                    logger.info(f"TTS auto-play initiated for response: {tts_message_id}")
            except Exception as tts_error:
                logger.error(f"TTS auto-play failed: {tts_error}")

        return JSONResponse({
            "response": response_text,
            "session_id": session_id,
            "tts_enabled": True,  # Always enabled for auto-play
            "tts_message_id": tts_message_id,
            "tts_priority": tts_priority
        })
    except Exception as e:
        logger.error(f"Chat error: {e}", exc_info=True)
        return JSONResponse({"error": str(e)}, status_code=500)

@app.post("/clear_history")
async def clear_history():
    # Frontend should manage and pass session ID to be safe
    session_id = str(uuid.uuid4())
    conversation_storage.clear_history(session_id)
    return {"message": "Conversation cleared", "session_id": session_id}

@app.get("/conversation_history")
async def get_history(session_id: str):
    history = conversation_storage.get_history(session_id)
    return {"history": history, "session_id": session_id}

@app.get("/inference_status")
async def inference_status():
    """Get detailed inference system status and prompt logging information"""
    from prompt_logger import prompt_logger
    prompt_stats = prompt_logger.get_prompt_stats()

    return {
        "inference_levels": {
            "level_1": {
                "name": "preva_Daeira_inference",
                "description": "Basic Recognition - What should I do?",
                "active": True,
                "prompt_saving": True
            },
            "level_2": {
                "name": "eva_Daeira_inference",
                "description": "Strategic Planning - What should I do? How should I do it?",
                "active": True,
                "prompt_saving": True
            },
            "level_3": {
                "name": "Daeira_inference",
                "description": "Deep Deliberation - Full cognitive processing",
                "active": True,
                "prompt_saving": True
            }
        },
        "prompt_logging": {
            "enabled": True,
            "prompt_directory": prompt_stats.get("prompt_save_dir"),
            "inference_directory": prompt_stats.get("inference_save_dir"),
            "total_prompts": prompt_stats.get("total_prompts", 0),
            "total_inferences": prompt_stats.get("total_inferences", 0),
            "recent_prompts": prompt_stats.get("recent_prompts", []),
            "recent_inferences": prompt_stats.get("recent_inferences", [])
        },
        "system_info": {
            "all_levels_active": True,
            "word_for_word_saving": True,
            "metadata_included": True,
            "session_tracking": True
        }
    }

@app.get("/system_status")
async def system_status():
    tts = get_tts_service()
    tts_status_info = tts.get_status() if tts else {"available": False}

    # Get prompt logging stats
    from prompt_logger import prompt_logger
    prompt_stats = prompt_logger.get_prompt_stats()

    return {
        "model_loaded": True,
        "memory_manager_available": memory_manager is not None,
        "document_processor_available": document_processor is not None,
        "document_processor_capabilities": document_processor.get_processing_capabilities() if document_processor else {},
        "tts_available": tts_status_info.get("available", False),
        "tts_status": tts_status_info,
        "tts_voice_preferences": ["susan", "hazel"],
        "tts_auto_play": True,  # Always enabled for model responses
        "email_service_available": manual_email_check is not None,
        "email_monitoring": "<EMAIL>",
        "email_capabilities": [
            "Receive and analyze emails",
            "Intelligent response generation",
            "Auto-triage and prioritization",
            "Memory integration"
        ],
        "inference_system": {
            "active_levels": ["preva_Daeira_inference", "eva_Daeira_inference", "Daeira_inference"],
            "prompt_saving_enabled": True,
            "prompt_save_directory": prompt_stats.get("prompt_save_dir"),
            "inference_save_directory": prompt_stats.get("inference_save_dir"),
            "total_prompts_saved": prompt_stats.get("total_prompts", 0),
            "total_inferences_saved": prompt_stats.get("total_inferences", 0)
        }
    }

# === DOCUMENT PROCESSING ENDPOINTS ===

@app.post("/upload_document")
async def upload_document(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    session_id: str = Form(default_factory=lambda: str(uuid.uuid4()))
):
    """Upload and process a document"""
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not available")

    try:
        # Create temporary file to save uploaded content
        temp_dir = "C:/DAEIRA/TEMP"
        os.makedirs(temp_dir, exist_ok=True)

        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
        temp_filename = f"{uuid.uuid4()}{file_extension}"
        temp_path = os.path.join(temp_dir, temp_filename)

        # Save uploaded file
        with open(temp_path, "wb") as temp_file:
            content = await file.read()
            temp_file.write(content)

        # Parse tags if provided
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

        # Add session tag
        tag_list.append(f"session_{session_id}")
        tag_list.append("uploaded")

        # Process the document
        doc_id = await document_processor.process_document(
            document_path=temp_path,
            document_title=title or file.filename,
            tags=tag_list
        )

        # Clean up temporary file
        try:
            os.remove(temp_path)
        except:
            pass  # Ignore cleanup errors

        return JSONResponse({
            "success": True,
            "document_id": doc_id,
            "filename": file.filename,
            "title": title or file.filename,
            "tags": tag_list,
            "session_id": session_id
        })

    except Exception as e:
        logger.error(f"Error uploading document: {e}", exc_info=True)
        # Clean up temporary file on error
        try:
            if 'temp_path' in locals():
                os.remove(temp_path)
        except:
            pass
        raise HTTPException(status_code=500, detail=f"Failed to process document: {str(e)}")

@app.post("/process_study_directory")
async def process_study_directory(
    use_cognitive: bool = Form(False),
    session_id: str = Form(default_factory=lambda: str(uuid.uuid4()))
):
    """Scan and process documents in the study directory"""
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not available")

    try:
        processed_ids = await document_processor.scan_study_directory(use_cognitive=use_cognitive)

        return JSONResponse({
            "success": True,
            "processed_count": len(processed_ids),
            "processed_document_ids": processed_ids,
            "cognitive_processing": use_cognitive,
            "session_id": session_id
        })

    except Exception as e:
        logger.error(f"Error processing study directory: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to process study directory: {str(e)}")

@app.post("/cognitive_document_query")
async def cognitive_document_query(
    query: str = Form(...),
    max_documents: int = Form(3),
    processing_focus: str = Form("comprehensive"),
    session_id: str = Form(default_factory=lambda: str(uuid.uuid4()))
):
    """Process documents with cognitive awareness for a specific query"""
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not available")

    try:
        result = await document_processor.cognitive_process_for_query(
            query=query,
            max_documents=max_documents,
            processing_focus=processing_focus
        )

        result["session_id"] = session_id
        return JSONResponse(result)

    except Exception as e:
        logger.error(f"Error in cognitive document query: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to process cognitive query: {str(e)}")

@app.get("/document_processing_stats")
async def get_document_processing_stats():
    """Get document processing statistics"""
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not available")

    try:
        stats = document_processor.get_processing_stats()
        return JSONResponse(stats)

    except Exception as e:
        logger.error(f"Error getting processing stats: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get processing stats: {str(e)}")

@app.post("/clear_document_caches")
async def clear_document_caches():
    """Clear all document processing caches"""
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not available")

    try:
        document_processor.clear_caches()
        return JSONResponse({
            "success": True,
            "message": "Document processing caches cleared"
        })

    except Exception as e:
        logger.error(f"Error clearing caches: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to clear caches: {str(e)}")

# === TTS ENDPOINTS ===

@app.post("/tts/speak")
async def tts_speak(
    text: str = Form(...),
    priority: str = Form("normal"),
    async_mode: bool = Form(True)
):
    """Speak text using TTS"""
    tts = get_tts_service()
    if not tts:
        raise HTTPException(status_code=503, detail="TTS service not available")

    try:
        tts_priority = get_tts_priority(priority)
        message_id = await tts.speak_text(text, tts_priority, async_mode)

        return JSONResponse({
            "success": True,
            "message_id": message_id,
            "text_length": len(text),
            "priority": priority
        })

    except Exception as e:
        logger.error(f"TTS speak error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"TTS speak failed: {str(e)}")

@app.post("/tts/control")
async def tts_control(action: str = Form(...)):
    """Control TTS playback (stop, clear_queue)"""
    tts = get_tts_service()
    if not tts:
        raise HTTPException(status_code=503, detail="TTS service not available")

    try:
        if action == "stop":
            success = tts.stop_speaking()
            return JSONResponse({
                "success": success,
                "action": action,
                "message": "TTS stopped" if success else "Failed to stop TTS"
            })
        elif action == "clear_queue":
            cleared_count = tts.clear_queue()
            return JSONResponse({
                "success": True,
                "action": action,
                "cleared_count": cleared_count,
                "message": f"Cleared {cleared_count} messages from queue"
            })
        else:
            raise HTTPException(status_code=400, detail=f"Unknown action: {action}")

    except Exception as e:
        logger.error(f"TTS control error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"TTS control failed: {str(e)}")

@app.get("/tts/status")
async def tts_status():
    """Get TTS system status"""
    tts = get_tts_service()
    if not tts:
        return JSONResponse({
            "available": False,
            "message": "TTS service not initialized"
        })

    try:
        status = tts.get_status()
        return JSONResponse(status)

    except Exception as e:
        logger.error(f"TTS status error: {e}", exc_info=True)
        return JSONResponse({
            "available": False,
            "error": str(e)
        })

@app.get("/tts/voices")
async def tts_voices():
    """Get available TTS voices"""
    tts = get_tts_service()
    if not tts:
        raise HTTPException(status_code=503, detail="TTS service not available")

    try:
        voices = tts.get_available_voices()
        return JSONResponse({
            "voices": voices,
            "count": len(voices),
            "selected_voice": tts.selected_voice,
            "voice_preferences": tts.voice_preferences
        })

    except Exception as e:
        logger.error(f"TTS voices error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")

@app.post("/tts/settings")
async def tts_update_settings(
    rate: Optional[int] = Form(None),
    volume: Optional[float] = Form(None),
    voice_id: Optional[str] = Form(None)
):
    """Update TTS settings"""
    tts = get_tts_service()
    if not tts:
        raise HTTPException(status_code=503, detail="TTS service not available")

    try:
        success = tts.update_settings(rate=rate, volume=volume, voice_id=voice_id)

        return JSONResponse({
            "success": success,
            "message": "TTS settings updated" if success else "Failed to update TTS settings",
            "settings": {
                "rate": rate,
                "volume": volume,
                "voice_id": voice_id
            }
        })

    except Exception as e:
        logger.error(f"TTS settings update error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to update TTS settings: {str(e)}")

# === EMAIL SERVICE ENDPOINTS ===

@app.post("/email/check")
async def manual_email_check_endpoint():
    """Manually trigger email checking"""
    if not manual_email_check:
        raise HTTPException(status_code=503, detail="Email service not available")

    try:
        # Run the manual email check
        manual_email_check()

        return JSONResponse({
            "success": True,
            "message": "Manual email check triggered",
            "timestamp": datetime.now(timezone.utc).isoformat()
        })

    except Exception as e:
        logger.error(f"Manual email check error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Email check failed: {str(e)}")

@app.get("/email/status")
async def email_service_status():
    """Get email service status and configuration"""
    try:
        from input import _load_config
        config = _load_config()

        return JSONResponse({
            "service_available": manual_email_check is not None,
            "monitoring_email": config.get('IMAP_USERNAME', 'Not configured'),
            "server": config.get('IMAP_SERVER', 'Not configured'),
            "polling_interval": config.get('POLLING_INTERVAL_SECONDS', 300),
            "folders": {
                "inbox": config.get('EMAIL_TARGET_FOLDER', 'INBOX'),
                "processed": config.get('EMAIL_PROCESSED_FOLDER', 'Processed/Daeira'),
                "errors": config.get('EMAIL_ERROR_FOLDER', 'Errors/Daeira')
            },
            "capabilities": [
                "IMAP email monitoring",
                "Intelligent email triage",
                "Automated response generation",
                "Memory integration",
                "SMTP response sending"
            ]
        })

    except Exception as e:
        logger.error(f"Email status error: {e}", exc_info=True)
        return JSONResponse({
            "service_available": False,
            "error": str(e)
        })

@app.post("/email/send_test")
async def send_test_email(
    to_address: str = Form(...),
    subject: str = Form("Test Email from DAEIRA"),
    message: str = Form("This is a test email from the DAEIRA email service.")
):
    """Send a test email"""
    if not manual_email_check:
        raise HTTPException(status_code=503, detail="Email service not available")

    try:
        # Create a test email response generator
        tokenizer, vl_gpt, _ = models
        email_generator = EmailResponseGenerator(vl_gpt, tokenizer, memory_manager)

        # Send the test email
        success = await email_generator.send_email_response(
            to_address=to_address,
            subject=subject,
            body=message
        )

        if success:
            return JSONResponse({
                "success": True,
                "message": f"Test email sent successfully to {to_address}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        else:
            raise HTTPException(status_code=500, detail="Failed to send test email")

    except Exception as e:
        logger.error(f"Test email error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Test email failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn

    logger.info("Starting DAEIRA server...")
    logger.info("Inference levels activated:")
    logger.info("  - preva_Daeira_inference (Basic Recognition)")
    logger.info("  - eva_Daeira_inference (Strategic Planning)")
    logger.info("  - Daeira_inference (Deep Deliberation)")
    logger.info("Prompt saving enabled for all levels")

    uvicorn.run(
        "daeira:app",
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    )
