import os
import json
import logging
from typing import Dict, List, Optional, Callable, Any, Tuple
from datetime import datetime, timezone

# Third-party imports
# Environment variables removed - using hardcoded configuration
import mysql.connector
from mysql.connector import pooling
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from pydantic import BaseModel, Field
from input import InputObject


logger = logging.getLogger(__name__)



def get_db_config() -> Dict[str, Any]:
    """
    Single source of truth for database configuration.
    All database connections should use this function.
    """
    return {
        "host": "localhost",
        "port": 3306,
        "user": "daeira",
        "password": "daeira",
        "database": "daeira",
        "auth_plugin": "mysql_native_password"
    }

def initialize_database(config: Dict[str, Any]):
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS InputObjects (
            event_id VARCHAR(36) PRIMARY KEY,
            timestamp DATETIME(6) NOT NULL,
            source_type VARCHAR(255) NOT NULL,
            source_details JSON NOT NULL,
            content_type VARCHAR(255) NOT NULL,
            content_payload LONGTEXT NOT NULL,
            priority INT NULL,
            initial_tags JSON NULL,
            processed_by_evaluate_timestamp DATETIME(6) NULL,
            evaluation_outcome_summary TEXT NULL,
            linked_event_ids JSON NULL,
            INDEX idx_timestamp (timestamp),
            INDEX idx_source_type (source_type),
            INDEX idx_priority (priority),
            INDEX idx_processed_timestamp (processed_by_evaluate_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        connection.commit()
        logger.info("Database schema verified/created successfully.")
    except mysql.connector.Error as err:
        logger.error(f"FATAL: Failed to initialize database: {err}")
        raise
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()


class StructuredMemoryStore:

    def __init__(self, config: Dict[str, Any]):
        logger.info("StructuredMemoryStore: initializing...")
        try:
            self.pool = pooling.MySQLConnectionPool(pool_name="daeira_pool",
                                                    pool_size=5,
                                                    **config)
            logger.info("Database connection pool established successfully.")
        except mysql.connector.Error as err:
            logger.error(f"FATAL: Failed to create database connection pool: {err}")
            raise

    def get_cursor(self, dictionary: bool = False):
        """
        Context manager for MySQL cursor.
        Usage:
            with self.get_cursor() as cursor:
                cursor.execute(...)
        """
        connection = self.pool.get_connection()
        cursor = connection.cursor(dictionary=dictionary)
        try:
            yield cursor
            connection.commit()
        except mysql.connector.Error as err:
            connection.rollback()
            logger.error(f"Database transaction failed: {err}")
            raise
        finally:
            cursor.close()
            connection.close()  # Returns the connection to the pool

    # Fix: Make get_cursor a context manager using contextlib
    from contextlib import contextmanager

    get_cursor = contextmanager(get_cursor)

    def row_to_input_object(self, row: Dict[str, Any]) -> 'InputObject':

        for key in ['source_details', 'initial_tags', 'linked_event_ids']:
            if isinstance(row.get(key), str):
                row[key] = json.loads(row[key])


        if row.get('content_type', '').startswith('application/json') and isinstance(row.get('content_payload'), str):
            row['content_payload'] = json.loads(row['content_payload'])

        init_args = {
            'event_id': row.get('event_id'),
            'timestamp': row.get('timestamp'),
            'source_type': row.get('source_type'),
            'source_details': row.get('source_details'),
            'content_type': row.get('content_type'),
            'content': row.get('content_payload'), # Map db 'content_payload' to 'content'
            'priority': row.get('priority'),
            'initial_tags': row.get('initial_tags')
        }
        return InputObject(**{k: v for k, v in init_args.items() if v is not None})


    def store_input_object(self, input_obj: 'InputObject') -> str:
        """Stores an InputObject in the database."""
        sql = """
            INSERT INTO InputObjects (
                event_id, timestamp, source_type, source_details, 
                content_type, content_payload, priority, initial_tags
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        content_payload = json.dumps(input_obj.content) if isinstance(input_obj.content, dict) else input_obj.content
        params = (
            input_obj.event_id, input_obj.timestamp, input_obj.source_type,
            json.dumps(input_obj.source_details), input_obj.content_type,
            content_payload, input_obj.priority,
            json.dumps(input_obj.initial_tags) if input_obj.initial_tags else None
        )
        
        with self.get_cursor() as cursor:
            cursor.execute(sql, params)
        
        logger.info(f"Stored InputObject with event_id: {input_obj.event_id}")
        return input_obj.event_id

    def get_input_object_by_id(self, event_id: str) -> Optional['InputObject']:
        """Retrieves an InputObject by its event_id."""
        with self.get_cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM InputObjects WHERE event_id = %s", (event_id,))
            result = cursor.fetchone()
        
        return self.row_to_input_object(result) if result else None

    def get_recent_input_objects(self, limit: int = 10, **filters) -> List['InputObject']:
        """Retrieves recent InputObjects with optional filtering."""
        query = "SELECT * FROM InputObjects"
        conditions = []
        params = []

        # Safely build WHERE clause
        for key, value in filters.items():
            if value is not None:
                # Basic whitelist to prevent injection on column names
                if key in ['source_type', 'before_timestamp']:
                    operator = '<' if key == 'before_timestamp' else '='
                    conditions.append(f"{key.replace('before_', '')} {operator} %s")
                    params.append(value)
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
            
        query += " ORDER BY timestamp DESC LIMIT %s"
        params.append(limit)
        
        with self.get_cursor(dictionary=True) as cursor:
            cursor.execute(query, tuple(params))
            results = cursor.fetchall()

        return [self.row_to_input_object(row) for row in results]

    def update_input_object_post_evaluation(self, event_id: str, outcome_summary: str, linked_ids: Optional[List[str]] = None):
        """Updates an InputObject after evaluation."""
        sql = """
            UPDATE InputObjects SET
                processed_by_evaluate_timestamp = %s,
                evaluation_outcome_summary = %s,
                linked_event_ids = %s
            WHERE event_id = %s
        """
        params = (
            datetime.now(timezone.utc),
            outcome_summary,
            json.dumps(linked_ids) if linked_ids else None,
            event_id
        )
        
        with self.get_cursor() as cursor:
            cursor.execute(sql, params)
        
        logger.info(f"Updated InputObject post-evaluation: {event_id}")


class ContextItem(BaseModel):
    """
    Standardized structure for context items returned during RAG operations.
    Used by the meta-cognitive layer for prompt construction.
    """
    content: str  # The actual text content
    source_type: str  # Where this content originated from
    relevance_score: float  # How relevant is this to the current query (0.0-1.0)
    timestamp: datetime  # When was this content created/stored
    author: Optional[str] = None  # Who created this content, if applicable
    title: Optional[str] = None  # Title or subject if applicable
    doc_id: str  # Unique identifier linking to the original source
    metadata: Dict[str, Any]  # Additional metadata that might be useful




class VectorMemoryStore:
    def __init__(self, chroma_config: Dict[str, Any]):
        self.config = chroma_config
        self.persistence_path =  "C:\\DAEIRA\\MEMORY"
        self.collection_name = "daeira_knowledge"
        self.embedding_model_name = "all-MiniLM-L6-v2"
        
        logger.info(f"VectorMemoryStore: initializing SentenceTransformer ({self.embedding_model_name})")

        self.embedding_model = SentenceTransformer(self.embedding_model_name)

        self.client = chromadb.PersistentClient(path="C:\\DAEIRA\\MEMORY")

        self.collection = self.client.get_or_create_collection(
            name=self.collection_name,

        )
        logger.info(f"VectorMemoryStore initialized with collection '{self.collection_name}'")
    
    def sanitize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        sanitized = {}
        for key, value in metadata.items():
            if isinstance(value, list):
                # Convert list to comma-separated string
                sanitized[key] = ', '.join(str(item) for item in value) if value else ""
            elif isinstance(value, dict):
                # Convert dict to JSON string
                sanitized[key] = json.dumps(value)
            elif value is None:
                # Keep None as is
                sanitized[key] = None
            elif isinstance(value, (str, int, float, bool)):
                # Keep scalar values as-is
                sanitized[key] = value
            else:
                # Convert other types to string
                sanitized[key] = str(value)
        return sanitized
    
    def add_text_embedding(self, text_content: str, doc_id: str, metadata: Dict[str, Any]):
        try:
            # Sanitize metadata before adding
            sanitized_metadata = self.sanitize_metadata(metadata)
            
            # Generate embedding
            embedding = self.embedding_model.encode(text_content).tolist()
            
            # Add to collection
            self.collection.add(
                embeddings=[embedding],
                documents=[text_content],
                ids=[doc_id],
                metadatas=[sanitized_metadata]
            )
            
        except Exception as e:
            logger.error(f"Failed to add text embedding: {e}")
            raise
    
    def semantic_search(
        self, 
        query_text: str, 
        top_k: int = 5, 
        filters: Optional[Dict] = None
    ) -> List[Dict]:
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode(query_text).tolist()
            
            # Search the collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=filters
            )
            
            # Process and return results
            processed_results = []
            if results['documents'] and len(results['documents']) > 0:
                for i, doc in enumerate(results['documents'][0]):
                    processed_results.append({
                        'document': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'id': results['ids'][0][i],
                        'distance': results['distances'][0][i] if 'distances' in results else None
                    })
            
            return processed_results
        except Exception as e:
            logger.error(f"Failed to perform semantic search: {e}")
            raise
    
    def delete_embeddings_by_parent_id(self, parent_event_id: str):

        try:
            # Delete from collection where metadata.parent_event_id matches
            self.collection.delete(
                where={"parent_event_id": parent_event_id}
            )
            
            logger.info(f"Deleted embeddings for parent_event_id: {parent_event_id}")
        except Exception as e:
            logger.error(f"Failed to delete embeddings: {e}")
            raise


class MemoryManagerDaeira:
    """
    Orchestrates the StructuredMemoryStore and VectorMemoryStore,
    providing a unified interface for DAEIRA's memory operations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        logger.info("MemoryManagerDaeira: initializing StructuredMemoryStore")
        """
        Initialize MemoryManagerDaeira with configuration.
        
        Args:
            config: Dictionary containing all memory configuration
        """
        self.config = config
        
        # Initialize structured and vector stores
        self.structured_store = StructuredMemoryStore(config.get("mysql", {}))
        logger.info("MemoryManagerDaeira: StructuredMemoryStore initialized")
        self.vector_store = VectorMemoryStore(config.get("chromadb", {}))
        logger.info("MemoryManagerDaeira: VectorMemoryStore initialized")
        logger.info("MemoryManagerDaeira initialized successfully")
    
    def record_input_event(self, input_obj: InputObject) -> str:
        """
        Record an input event in both structured and vector stores.
        
        Args:
            input_obj: The InputObject to record
            
        Returns:
            str: The event_id of the recorded object
        """
        # Store in structured store
        event_id = self.structured_store.store_input_object(input_obj)
        
        # Check if content is suitable for embedding
        if isinstance(input_obj.content, str) and input_obj.content_type in [
            'text/plain', 'text/markdown', 'text/html', 'application/json'
        ]:
            # Prepare vector metadata
            vector_metadata = {
                "parent_event_id": event_id,
                "source_type": input_obj.source_type,
                "timestamp": input_obj.timestamp.isoformat(),
                "content_type": input_obj.content_type,
                "tags": input_obj.initial_tags if input_obj.initial_tags else []
            }
            
            # Add source details to metadata
            for key, value in input_obj.source_details.items():
                if isinstance(value, (str, int, float, bool)):
                    vector_metadata[f"source_{key}"] = value
            
            # Add preview of content
            content_text = input_obj.content
            if isinstance(content_text, dict):
                content_text = json.dumps(content_text)
            preview_length = min(200, len(content_text))
            vector_metadata["content_preview"] = content_text[:preview_length]
            
            # Store in vector store (metadata will be sanitized automatically)
            self.vector_store.add_text_embedding(
                text_content=content_text,
                doc_id=event_id,
                metadata=vector_metadata
            )
        
        return event_id
    
    def retrieve_context_for_task(
        self, 
        current_task_description: str,
        conversation_history: List[InputObject],
        max_tokens: int,
        context_config: Optional[Dict] = None
    ) -> List[ContextItem]:
        """
        Retrieve relevant context for a task, combining semantic search results
        and recent conversation history.
        
        Args:
            current_task_description: Description of the current task
            conversation_history: Recent conversation history
            max_tokens: Maximum tokens for context assembly
            context_config: Configuration for context retrieval
            
        Returns:
            List[ContextItem]: Assembled context items
        """
        if context_config is None:
            context_config = {}
        
        # Set defaults for context_config
        semantic_results_count = context_config.get("semantic_results_count", 5)
        history_inclusion_count = context_config.get("history_inclusion_count", 3)
        metadata_filters = context_config.get("metadata_filters", None)
        
        # 1. Perform semantic search using the task description
        semantic_results = self.vector_store.semantic_search(
            query_text=current_task_description,
            top_k=semantic_results_count,
            filters=metadata_filters
        )
        
        # 2. Process conversation history
        history_items = []
        for item in conversation_history[-history_inclusion_count:]:
            if isinstance(item.content, dict):
                content_text = json.dumps(item.content)
            else:
                content_text = item.content
                
            history_items.append({
                "content": content_text,
                "event_id": item.event_id,
                "timestamp": item.timestamp,
                "source_type": item.source_type,
                "metadata": item.source_details
            })
        
        # 3. Combine and prioritize results
        combined_results = []
        
        # Add semantic search results
        for result in semantic_results:
            # Calculate relevance score (normalize distance to 0-1 range)
            relevance_score = 1.0 - (result.get('distance', 0) / 2.0) if result.get('distance') is not None else 0.9
            
            # Extract metadata
            metadata = result.get('metadata', {})
            timestamp_str = metadata.get('timestamp')
            timestamp = datetime.fromisoformat(timestamp_str) if timestamp_str else datetime.now(timezone.utc)
            
            # Create context item
            context_item = ContextItem(
                content=result['document'],
                source_type=metadata.get('source_type', 'unknown'),
                relevance_score=relevance_score,
                timestamp=timestamp,
                author=metadata.get('source_from', None),
                title=metadata.get('source_subject_or_title', None),
                doc_id=result['id'],
                metadata=metadata
            )
            combined_results.append(context_item)
        
        # Add conversation history
        for item in history_items:
            metadata = item.get('metadata', {})
            
            context_item = ContextItem(
                content=item['content'],
                source_type=item['source_type'],
                relevance_score=1.0,  # History items are highly relevant by default
                timestamp=item['timestamp'],
                author=metadata.get('from', None),
                title=metadata.get('subject', None),
                doc_id=item['event_id'],
                metadata=metadata
            )
            combined_results.append(context_item)
        
        # Sort by relevance and recency
        combined_results.sort(key=lambda x: (x.relevance_score, x.timestamp), reverse=True)
        
        # TODO: Implement token counting and truncation to respect max_tokens
        # This would require a tokenizer implementation
        
        return combined_results
    
    def add_document_to_memory(
        self,
        document_content: str,
        document_metadata: Dict[str, Any],
        chunking_strategy: Optional[Callable] = None
    ):
        """
        Add a document to memory, potentially chunking it into smaller pieces.
        
        Args:
            document_content: The document content
            document_metadata: Metadata about the document
            chunking_strategy: Optional function to chunk the document
        """
        # Create InputObject for the document
        document_input = InputObject(
            source_type='studied_document',
            source_details=document_metadata,
            content_type='text/plain',  # Adjust based on document type
            content=document_content,
            initial_tags=['document']
        )
        
        # Store in structured store
        parent_event_id = self.structured_store.store_input_object(document_input)
        
        # Determine chunks
        if chunking_strategy:
            chunks = chunking_strategy(document_content)
        else:
            chunks = [document_content]  # No chunking, use whole document
        
        # Add each chunk to vector store
        for i, chunk in enumerate(chunks):
            # Create chunk-specific metadata
            chunk_metadata = {
                "parent_event_id": parent_event_id,
                "doc_chunk_id": f"{parent_event_id}_chunk_{i}",
                "source_type": "document_chunk_study",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "content_type": "text/plain",
                "tags": document_input.initial_tags if document_input.initial_tags else []
            }
            
            # Add document metadata
            for key, value in document_metadata.items():
                if isinstance(value, (str, int, float, bool)):
                    chunk_metadata[f"doc_{key}"] = value
            
            # Add content preview
            preview_length = min(200, len(chunk))
            chunk_metadata["content_preview"] = chunk[:preview_length]
            
            # Generate chunk ID
            chunk_id = f"{parent_event_id}_chunk_{i}"
            
            # Store in vector store (metadata will be sanitized automatically)
            self.vector_store.add_text_embedding(
                text_content=chunk,
                doc_id=chunk_id,
                metadata=chunk_metadata
            )
        
        logger.info(f"Added document to memory with {len(chunks)} chunks, parent_event_id: {parent_event_id}")
        return parent_event_id


# REMOVED: Duplicate configuration - using get_db_config() as single source of truth


# Example initialization code (not executed)
def initialize_memory():
    """
    Initialize the memory system using centralized configuration.
    """
    # Use centralized database configuration
    db_config = get_db_config()

    # Configure memory system using single source of truth
    memory_config = {
        "mysql": db_config,
        "chromadb": {
            "persistence_path": "C:/DAEIRA/MEMORY",
            "collection_name": "daeira_knowledge_base",
            "embedding_model": "all-MiniLM-L6-v2"
        }
    }

    # Validate required configuration
    if not memory_config["mysql"]["password"]:
        raise ValueError("MYSQL_PASSWORD environment variable is required")

    # Create memory manager
    memory_manager = MemoryManagerDaeira(memory_config)
    return memory_manager


if __name__ == "__main__":
    # For testing or standalone execution
    logging.basicConfig(level=logging.INFO)
    memory_manager = initialize_memory()
    logger.info("MEMORY_dahlia.py initialized and ready.")
