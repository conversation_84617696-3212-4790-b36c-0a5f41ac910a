# DAEIRA Codebase Fix Action Plan

## Priority 1: Critical Fixes (Must Fix Immediately)

### 1. Fix Missing `three_inference_daeira_processing` Function
**Files**: `APP/email_service.py`, `APP/main.py`
**Action**: Create the missing function or update imports
**Estimated Time**: 2-3 hours

### 2. Fix Import Path in evaluate.py
**File**: `APP/evaluate.py`
**Action**: Change `from .input import InputObject` to `from input import InputObject`
**Estimated Time**: 5 minutes

### 3. Move Hardcoded Credentials to Environment Variables
**Files**: `APP/input.py`, `APP/main.py`, `APP/MEMORY_daeira.py`
**Action**: Create `.env` file and update code to use environment variables
**Estimated Time**: 30 minutes

### 4. Enable Cognitive Cycles in evaluate.py ✅ COMPLETED
**File**: `APP/evaluate.py`
**Action**: Uncomment cycles 1 and 2, ensure they work properly
**Status**: ✅ All three inference levels now active: preva_Daeira_inference, eva_Daeira_inference, Daeira_inference

## Priority 2: High Impact Fixes (Fix This Week)

### 5. Update All DAEIRA References ✅ COMPLETED
**Files**: `APP/main.py`, `APP/MEMORY_daeira.py`, and others
**Action**: Global find/replace and path updates
**Status**: ✅ Renamed throughout codebase: DAHLIA→DAEIRA, Dahlia→Daeira, dahlia→daeira

### 6. Fix Cognitive Processor Null References
**File**: `APP/document_processor.py`
**Action**: Add proper null checks and error handling
**Estimated Time**: 1 hour

### 7. Standardize Database Configuration ✅ COMPLETED
**Files**: `APP/main.py`, `APP/MEMORY_daeira.py`
**Action**: Create unified database config system
**Status**: ✅ Consolidated 4 duplicate credential definitions into single source of truth

### 8. Fix Deprecated datetime.utcnow() Usage
**Files**: Multiple files
**Action**: Replace with `datetime.now(timezone.utc)`
**Estimated Time**: 30 minutes

## Priority 3: Medium Impact Fixes (Fix This Month)

### 9. Implement HTML to Text Conversion
**File**: `APP/input.py`
**Action**: Add HTML parsing for email content
**Estimated Time**: 2 hours

### 10. Add Comprehensive Error Handling
**Files**: Multiple files
**Action**: Add try-catch blocks and proper error responses
**Estimated Time**: 4-6 hours

### 11. Centralize Logging Configuration
**Files**: Multiple files
**Action**: Create unified logging setup
**Estimated Time**: 1 hour

### 12. Remove Unused Code and Imports
**Files**: `APP/src/App.jsx` and others
**Action**: Clean up unused functions and imports
**Estimated Time**: 1 hour

## Priority 4: Long-term Improvements (Plan for Next Sprint)

### 13. Implement Configuration Management System
**Action**: Create centralized config with validation
**Estimated Time**: 1 day

### 14. Add Comprehensive Type Hints
**Action**: Add type hints throughout Python codebase
**Estimated Time**: 2-3 days

### 15. Implement Proper Testing Suite
**Action**: Add unit tests, integration tests, and security tests
**Estimated Time**: 1 week

### 16. Refactor Architecture for Better Separation
**Action**: Reduce coupling, implement dependency injection
**Estimated Time**: 1-2 weeks

## Immediate Action Items (Start Today)

### Step 1: Create Environment Configuration
```bash
# Create .env file
touch APP/.env

# Add to .env:
MYSQL_HOST=localhost
MYSQL_USER=daeira
MYSQL_PASSWORD=your_secure_password
MYSQL_DATABASE=daeira
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your_email_password
CHROMADB_PATH=C:/DAEIRA/MEMORY
```

### Step 2: Fix Critical Import Issue
```python
# In APP/evaluate.py, line 10:
# Change: from .input import InputObject
# To: from input import InputObject
```

### Step 3: Create Missing Function Stub
```python
# Add to APP/main.py or create new file:
async def three_inference_daeira_processing(victor_prompt: str, enable_tts: bool = False):
    """Temporary stub - implement full 3-inference system"""
    # Use existing inference system as fallback
    return "Response", "Thinking", {}
```

### Step 4: Enable Cognitive Cycles
```python
# In APP/evaluate.py, uncomment lines 37-48:
cycle_1_response = await _cognitive_cycle_1(...)
cycle_2_response = await _cognitive_cycle_2(...)
```

## Testing Plan After Fixes

### 1. Basic Functionality Tests
- [ ] Start DAEIRA server without errors
- [ ] Send chat message and receive response
- [ ] Upload document successfully
- [ ] Check email service status

### 2. Integration Tests
- [ ] Email processing end-to-end
- [ ] Document processing with memory storage
- [ ] TTS functionality
- [ ] Memory retrieval and search

### 3. Error Handling Tests
- [ ] Invalid inputs handled gracefully
- [ ] Network failures handled properly
- [ ] Database connection issues handled
- [ ] Missing files handled correctly

## Risk Assessment

### High Risk Items:
- **Database schema changes**: Could break existing data
- **Authentication changes**: Could lock out users
- **Core inference changes**: Could break main functionality

### Medium Risk Items:
- **Configuration changes**: Might require manual setup
- **Import path changes**: Could break in different environments
- **Logging changes**: Might affect debugging

### Low Risk Items:
- **Code cleanup**: Minimal impact on functionality
- **Type hints**: No runtime impact
- **Documentation updates**: No functional impact

## Success Criteria

### Phase 1 Complete When:
- [ ] No critical errors on startup
- [ ] Basic chat functionality works
- [ ] Email service initializes without errors
- [ ] Document upload works

### Phase 2 Complete When:
- [ ] All DAHLIA references updated to DAEIRA
- [ ] Cognitive processing fully functional
- [ ] Comprehensive error handling in place
- [ ] Security vulnerabilities addressed

### Phase 3 Complete When:
- [ ] Full test suite passing
- [ ] Performance optimizations implemented
- [ ] Documentation complete
- [ ] Code quality metrics met

## Timeline Estimate

- **Week 1**: Priority 1 fixes (critical issues)
- **Week 2**: Priority 2 fixes (high impact)
- **Week 3-4**: Priority 3 fixes (medium impact)
- **Month 2**: Priority 4 improvements (long-term)

## Resources Needed

- **Development Time**: ~40-60 hours total
- **Testing Time**: ~20-30 hours
- **Documentation Time**: ~10-15 hours
- **Code Review Time**: ~10-15 hours

Total estimated effort: **80-120 hours** (2-3 weeks full-time)
